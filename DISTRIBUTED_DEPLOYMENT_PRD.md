# PRD: 分布式高可用部署方案

**版本:** 2.0
**日期:** 2025年7月25日
**作者:** Gemini

---

## 1. 背景与目标

### 1.1. 背景

当前 `cf-clearance-scraper` 服务以单实例模式运行，这带来了两个核心挑战：

1.  **性能瓶颈**: 随着请求量的增加，单个服务器的 CPU 和内存资源将成为瓶颈，严重限制了服务的最大并发处理能力和整体吞吐量。
2.  **单点故障 (SPOF)**: 如果该服务器或应用进程发生故障，整个服务将完全中断，导致可用性无法得到保障，对业务连续性构成威胁。

为了解决这些问题，必须将服务架构从单点部署升级为一套健壮、高可用的分布式集群部署方案。

### 1.2. 核心目标

- **提升性能与并发能力**: 通过水平扩展服务节点，将请求压力智能地分摊到多个服务器上，实现接近线性的性能增长，以支持大规模并发请求。
- **提高服务可用性**: 建立一个无单点故障的架构。当集群中的某个节点失效时，系统能够自动、快速地隔离故障节点，并将流量无缝切换到健康的节点上，保证服务的持续可用性。
- **增强系统可扩展性**: 设计一套易于扩展的弹性架构。未来可以根据业务增长的需求，通过简单地增加新的服务节点来平滑地提升系统容量，无需改动核心架构。
- **实现全面可观测性**: 建立一套强大的监控系统，能够实时收集和展示集群中每个节点的核心状态指标（如 CPU、内存使用率、活跃连接数等），并能在节点发生故障时自动进行进程守护和重启。

---

## 2. 方案设计

### 2.1. 核心架构：对等节点与智能负载均衡

我们将采用业界成熟的 **反向代理 + 对等节点集群** 的模式。此架构中，所有节点职责对等，大大简化了系统的复杂性并提高了整体的健壮性。

- **负载均衡器 (Load Balancer)**: 作为整个集群的唯一流量入口，负责接收所有外部请求，并将其智能地分发给后端的服务节点。
- **服务节点集群 (Worker Nodes)**: 一个由多个运行相同服务实例的服务器组成的集群。每个节点都是对等的，可以独立处理任何请求，并通过进程管理器实现自我修复。

### 2.2. 架构图

```
        +---------------------------------+
        |      API 调用客户端 (Clients)   |
        +----------------+----------------+
                         |
                         | API 请求 (e.g., http://<Nginx_IP>:8080/)
                         v
        +----------------+----------------+
        |      负载均衡器 (Nginx)         |
        |   - 反向代理 & 流量分发         |
        |   - SSL 终止 (可选)             |
        |   - 被动健康检查               |
        +----------------+----------------+
                         |
           +-------------+-------------+-------------+
           |             |             |
           v             v             v
+----------v---------+ +---------v----------+ +---------v----------+
|  服务节点 1 (Node) | |  服务节点 2 (Node) | |  服务节点 3 (Node) |
|  - PM2 进程守护    | |  - PM2 进程守护    | |  - PM2 进程守护    |
|  - 运行应用服务    | |  - 运行应用服务    | |  - 运行应用服务    |
|  - 上报监控指标    | |  - 上报监控指标    | |  - 上报监控指标    |
+--------------------+ +--------------------+ +--------------------+
```

### 2.3. 技术选型

- **负载均衡器**: **Nginx**。高性能、高稳定性的开源软件，是反向代理和负载均衡场景下的事实标准。
- **进程管理与守护**: **PM2**。一个功能强大的 Node.js 进程管理器，内置自动重启、日志管理、性能监控和单机集群功能，是保障节点健壮性的关键。
- **监控与可视化**: **Prometheus** + **Grafana**。业界领先的开源监控解决方案，用于实现对所有节点性能指标的集中采集和可视化展示。

---

## 3. 详细实施步骤

### 阶段一：服务节点部署 (在所有工作节点上重复执行)

1.  **克隆代码库与安装依赖**:
    ```bash
    git clone https://github.com/0xsongsu/cf-clearance-scraper.git
    cd cf-clearance-scraper
    npm install
    ```

2.  **集成监控指标 (Prometheus Exporter)**:
    - 安装 `prom-client` 库: `npm install prom-client`
    - 修改应用入口文件 (`src/index.js`)，注入监控逻辑以暴露 `/metrics` 端点：

    ```javascript
    // 在 src/index.js 的头部添加
    const client = require('prom-client');

    // 在 app 实例创建后，路由定义前添加
    // 启用默认的 Node.js 指标 (CPU, memory, event loop lag, etc.)
    const collectDefaultMetrics = client.collectDefaultMetrics;
    collectDefaultMetrics({ timeout: 5000 });

    // 暴露 /metrics 端点给 Prometheus 抓取
    app.get('/metrics', async (req, res) => {
      try {
        res.set('Content-Type', client.register.contentType);
        res.end(await client.register.metrics());
      } catch (ex) {
        res.status(500).end(ex);
      }
    });
    ```

3.  **使用 PM2 实现进程守护与自动重启**:
    - 全局安装 PM2: `npm install pm2 -g`
    - 在项目根目录创建 `ecosystem.config.js` 配置文件，这是管理应用的核心：

    ```javascript
    module.exports = {
      apps : [{
        name        : 'cf-clearance-worker', // 应用名称
        script      : 'start.js',          // 启动脚本
        instances   : 1,                   // 每个节点只运行一个实例
        autorestart : true,                // [核心] 启用自动重启，应用崩溃后会自动拉起
        watch       : false,               // 不建议在生产环境开启 watch
        max_memory_restart: '1G',          // [核心] 当内存超过1G时自动重启，防止内存泄漏
        env_production: {
          NODE_ENV: 'production',
          PORT: 3000                     // 定义生产环境端口
        }
      }]
    };
    ```
    - **启动应用**: 使用 PM2 以生产模式启动应用。
      ```bash
      pm2 start ecosystem.config.js --env production
      ```
    - **设置开机自启**: 运行 `pm2 startup` 并按提示操作，确保服务器重启后应用能自动恢复。

### 阶段二：负载均衡器部署 (在管理节点上执行)

1.  **安装 Nginx**:
    ```bash
    # 以 Debian/Ubuntu 为例
    sudo apt-get update && sudo apt-get install -y nginx
    ```

2.  **配置 Nginx**:
    - 创建一个新的配置文件: `sudo nano /etc/nginx/sites-available/cf_cluster`
    - 将以下配置粘贴进去，并**修改工作节点的IP地址**：

    ```nginx
    # 定义上游服务节点集群
    upstream cf_workers {
        # 负载均衡策略: round_robin (轮询，默认) 或 least_conn (最少连接)
        # least_conn;

        # --- 在这里填入您所有工作节点的真实局域网IP --- #
        server *************:3000;
        server *************:3000;
        # server *************:3000; # 如果有更多，继续添加
    }

    server {
        listen 8080; # 定义外部访问端口
        server_name _; # 接受所有指向此端口的请求

        location / {
            proxy_pass http://cf_workers;

            # 设置代理头信息，以便后端服务能获取真实客户端信息
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;

            # [核心] 被动健康检查：当一个节点返回错误或超时，Nginx会自动尝试下一个节点
            proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
            proxy_connect_timeout 2s;
            proxy_read_timeout 300s;
        }
    }
    ```
    - 启用该配置: `sudo ln -s /etc/nginx/sites-available/cf_cluster /etc/nginx/sites-enabled/`

3.  **测试并启动 Nginx**:
    - 测试配置语法: `sudo nginx -t`
    - 重启 Nginx 使配置生效: `sudo systemctl restart nginx`

### 阶段三：监控系统部署 (可在独立服务器或管理节点上执行)

1.  **安装并配置 Prometheus**:
    - 从官网下载并解压 Prometheus。
    - 修改 `prometheus.yml` 配置文件，添加抓取任务：

    ```yaml
    global:
      scrape_interval: 15s # 每15秒抓取一次

    scrape_configs:
      - job_name: 'cf-clearance-workers'
        static_configs:
          # --- 在这里填入您所有工作节点的IP和端口 --- #
          - targets: ['*************:3000', '*************:3000']
    ```
    - 启动 Prometheus: `./prometheus --config.file=prometheus.yml`

2.  **安装并配置 Grafana**:
    - 从官网下载并安装 Grafana。
    - 启动 Grafana 服务 (`sudo systemctl start grafana-server`)。
    - 浏览器访问 `http://<Grafana_IP>:3000`，使用默认账号密码 (admin/admin) 登录。
    - 在 "Configuration > Data Sources" 中添加 Prometheus 为数据源 (URL 通常是 `http://localhost:9090`)。
    - 创建新的仪表盘，添加图表并使用 PromQL 查询语言（如 `rate(process_cpu_seconds_total[1m])`）来可视化展示各个节点的性能数据。

---

## 4. 验证与测试

1.  **API 功能验证**: 将客户端请求的目标地址指向负载均衡器 (`http://<Nginx_IP>:8080`)，确认 API 功能正常。
2.  **负载均衡验证**: 连续发送多个请求，通过 `pm2 logs` 或 Grafana 仪表盘确认请求被均匀分发到了不同的节点上。
3.  **故障自愈验证**: 
    - **进程级故障**: 手动杀死一个节点上的 Node.js 进程 (`kill <PID>`)，观察 PM2 是否在几秒内自动重启了该进程 (`pm2 list`)
    - **节点级故障**: 手动停止一个工作节点的服务 (`pm2 stop cf-worker`) 或关闭服务器。再次发送请求，确认服务仍然可用，并且 Nginx 不再将请求发往故障节点。之后重启该节点，确认它能被 Nginx 自动重新纳入服务集群。
4.  **监控数据验证**: 在 Grafana 中查看仪表盘，确认能清晰地看到来自所有节点的 CPU、内存和请求数等关键指标。