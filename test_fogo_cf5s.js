/**
 * 测试 faucet.fogo.io 的 cf5s 自动点击功能
 */

const axios = require('axios');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  testUrl: 'https://faucet.fogo.io/',
  timeout: 180000, // 3分钟超时，给足时间处理验证
};

/**
 * 测试 fogo.io 的 cf5s 功能
 */
async function testFogoCf5s() {
  console.log('🚀 测试 faucet.fogo.io 的 CF5S 自动点击功能');
  console.log('=' .repeat(50));
  console.log(`🌐 测试网站: ${TEST_CONFIG.testUrl}`);
  console.log(`⏱️  超时设置: ${TEST_CONFIG.timeout / 1000}秒`);
  console.log('');

  const startTime = Date.now();

  try {
    console.log('📤 发送 cf5s 请求...');
    
    const response = await axios.post(TEST_CONFIG.baseUrl, {
      type: 'cf5s',
      websiteUrl: TEST_CONFIG.testUrl
    }, {
      timeout: TEST_CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log('\n📊 响应结果:');
    console.log('=' .repeat(30));

    if (response.data.code === 200) {
      console.log('✅ CF5S 自动验证成功!');
      console.log(`⏱️  总处理时间: ${duration}ms (${(duration / 1000).toFixed(1)}秒)`);
      
      // 显示获取到的数据
      if (response.data.cf_clearance) {
        console.log(`🍪 cf_clearance: ${response.data.cf_clearance.substring(0, 30)}...`);
        console.log(`📏 Cookie长度: ${response.data.cf_clearance.length} 字符`);
      }
      
      if (response.data.headers) {
        console.log(`🌐 User-Agent: ${response.data.headers['User-Agent']?.substring(0, 60)}...`);
        console.log(`📋 请求头数量: ${Object.keys(response.data.headers).length}`);
      }
      
      if (response.data.cookies) {
        console.log(`🍪 总Cookie数量: ${response.data.cookies.length}`);
        
        // 显示主要的cookies
        const importantCookies = response.data.cookies.filter(cookie => 
          cookie.name.includes('cf_') || 
          cookie.name.includes('clearance') ||
          cookie.name.includes('session')
        );
        
        if (importantCookies.length > 0) {
          console.log('🔑 重要Cookies:');
          importantCookies.forEach(cookie => {
            console.log(`   - ${cookie.name}: ${cookie.value.substring(0, 20)}...`);
          });
        }
      }
      
      console.log(`🌐 目标URL: ${response.data.url}`);
      console.log(`📅 时间戳: ${response.data.timestamp}`);
      
      // 性能评估
      if (duration < 30000) {
        console.log('🚀 性能评估: 优秀 (< 30秒)');
      } else if (duration < 60000) {
        console.log('⚡ 性能评估: 良好 (30-60秒)');
      } else if (duration < 120000) {
        console.log('⏳ 性能评估: 一般 (1-2分钟)');
      } else {
        console.log('🐌 性能评估: 较慢 (> 2分钟)');
      }

    } else {
      console.log(`❌ CF5S 验证失败`);
      console.log(`📝 错误代码: ${response.data.code}`);
      console.log(`📝 错误消息: ${response.data.message}`);
      console.log(`⏱️  失败时间: ${duration}ms`);
    }

  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('\n❌ 测试出错');
    console.log('=' .repeat(30));
    console.log(`⏱️  错误时间: ${duration}ms (${(duration / 1000).toFixed(1)}秒)`);
    console.log(`📝 错误类型: ${error.code || 'Unknown'}`);
    console.log(`📝 错误消息: ${error.message}`);
    
    if (error.response) {
      console.log(`📊 HTTP状态: ${error.response.status}`);
      console.log(`📊 响应数据:`, JSON.stringify(error.response.data, null, 2));
    }
    
    // 错误分析
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 解决建议: 请确保服务已启动 (npm start)');
    } else if (error.code === 'ETIMEDOUT' || error.message.includes('timeout')) {
      console.log('\n💡 解决建议: 网站响应较慢，可以尝试:');
      console.log('   1. 增加超时时间');
      console.log('   2. 检查网络连接');
      console.log('   3. 稍后重试');
    } else if (error.response?.status === 400) {
      console.log('\n💡 解决建议: 请求参数可能有误，检查URL格式');
    } else if (error.response?.status >= 500) {
      console.log('\n💡 解决建议: 服务器内部错误，检查服务日志');
    }
  }
}

/**
 * 检查服务状态
 */
async function checkServiceStatus() {
  try {
    console.log('🔍 检查服务状态...');
    const response = await axios.get(`${TEST_CONFIG.baseUrl}/health`, {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log('✅ 服务运行正常');
      return true;
    } else {
      console.log('❌ 服务状态异常');
      return false;
    }
  } catch (error) {
    console.log(`❌ 无法连接到服务: ${error.message}`);
    console.log('💡 请确保服务已启动: npm start');
    console.log('💡 默认端口: http://localhost:3000');
    return false;
  }
}

/**
 * 显示测试信息
 */
function showTestInfo() {
  console.log('📋 测试信息');
  console.log('=' .repeat(30));
  console.log('🎯 测试目标: faucet.fogo.io Cloudflare验证');
  console.log('🔧 测试类型: cf5s (Cloudflare 5秒盾绕过)');
  console.log('🤖 自动点击: 启用 (自动处理验证复选框)');
  console.log('📊 预期结果: 获取cf_clearance cookie和完整请求头');
  console.log('');
  
  console.log('🔍 自动点击功能将尝试:');
  console.log('   1. 检测页面中的验证元素');
  console.log('   2. 自动点击"确认您是真实用户"复选框');
  console.log('   3. 等待Cloudflare验证完成');
  console.log('   4. 获取cf_clearance cookie');
  console.log('');
}

/**
 * 主函数
 */
async function main() {
  console.log('🧪 Faucet.fogo.io CF5S 自动点击测试');
  console.log('🔗 https://github.com/0xsongsu/cf-clearance-scraper');
  console.log('📅 ' + new Date().toLocaleString());
  console.log('');
  
  // 显示测试信息
  showTestInfo();
  
  // 检查服务状态
  const serviceOk = await checkServiceStatus();
  if (!serviceOk) {
    process.exit(1);
  }
  
  console.log('');
  
  // 运行测试
  await testFogoCf5s();
  
  console.log('\n🎉 测试完成!');
  console.log('');
  console.log('💡 提示:');
  console.log('   - 如果测试失败，请查看服务日志了解详细信息');
  console.log('   - 可以访问 http://localhost:3000/monitor 查看实时状态');
  console.log('   - 自动点击配置文件: captcha-solvers/turnstile/config/autoClick.js');
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 测试运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testFogoCf5s,
  checkServiceStatus
};
