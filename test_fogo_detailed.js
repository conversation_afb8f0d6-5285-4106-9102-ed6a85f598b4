/**
 * 详细测试 faucet.fogo.io 的 cf5s 自动点击功能
 * 显示完整的响应数据和自动点击过程
 */

const axios = require('axios');

async function testFogoDetailed() {
  console.log('🔬 详细测试 faucet.fogo.io CF5S 自动点击功能');
  console.log('=' .repeat(60));
  
  try {
    const startTime = Date.now();
    
    console.log('📤 发送请求到 https://faucet.fogo.io/...');
    
    const response = await axios.post('http://localhost:3000', {
      type: 'cf5s',
      websiteUrl: 'https://faucet.fogo.io/'
    }, {
      timeout: 180000
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log('\n📊 完整响应数据:');
    console.log('=' .repeat(40));
    console.log(JSON.stringify(response.data, null, 2));
    
    console.log('\n📈 性能分析:');
    console.log('=' .repeat(40));
    console.log(`⏱️  总耗时: ${duration}ms (${(duration/1000).toFixed(1)}秒)`);
    console.log(`📊 响应代码: ${response.data.code}`);
    console.log(`📝 响应消息: ${response.data.message || 'N/A'}`);
    
    if (response.data.cf_clearance) {
      console.log('\n🍪 Cookie 分析:');
      console.log('=' .repeat(40));
      console.log(`🔑 cf_clearance: ${response.data.cf_clearance}`);
      console.log(`📏 Cookie长度: ${response.data.cf_clearance.length} 字符`);
      
      // 验证cookie格式
      if (response.data.cf_clearance.length > 100) {
        console.log('✅ Cookie长度正常 (>100字符)');
      } else {
        console.log('⚠️  Cookie长度较短，可能不完整');
      }
    }
    
    if (response.data.headers) {
      console.log('\n🌐 请求头分析:');
      console.log('=' .repeat(40));
      Object.entries(response.data.headers).forEach(([key, value]) => {
        console.log(`   ${key}: ${value}`);
      });
    }
    
    if (response.data.cookies && Array.isArray(response.data.cookies)) {
      console.log('\n🍪 所有Cookies:');
      console.log('=' .repeat(40));
      response.data.cookies.forEach((cookie, index) => {
        console.log(`${index + 1}. ${cookie.name}: ${cookie.value.substring(0, 30)}${cookie.value.length > 30 ? '...' : ''}`);
        console.log(`   Domain: ${cookie.domain}, Path: ${cookie.path}`);
        console.log(`   Secure: ${cookie.secure}, HttpOnly: ${cookie.httpOnly}`);
        console.log('');
      });
    }
    
    // 成功指标
    console.log('\n✅ 成功指标:');
    console.log('=' .repeat(40));
    console.log(`🎯 自动点击: ${duration < 60000 ? '成功 (快速完成)' : '成功 (较慢)'}`);
    console.log(`🍪 Cookie获取: ${response.data.cf_clearance ? '成功' : '失败'}`);
    console.log(`📋 请求头: ${response.data.headers ? '成功' : '失败'}`);
    console.log(`⚡ 性能: ${duration < 15000 ? '优秀' : duration < 30000 ? '良好' : '一般'}`);
    
  } catch (error) {
    console.log('\n❌ 测试失败');
    console.log('=' .repeat(40));
    console.log(`错误: ${error.message}`);
    
    if (error.response) {
      console.log(`HTTP状态: ${error.response.status}`);
      console.log('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
  }
}

// 运行测试
testFogoDetailed().then(() => {
  console.log('\n🎉 详细测试完成!');
}).catch(error => {
  console.error('❌ 测试运行失败:', error.message);
});
