# 自动点击功能说明

## 概述

CF Clearance Scraper 现在支持自动点击"确认您是真实用户"复选框功能，解决了cf5s和cftoken服务中需要手动点击验证的问题。

## 功能特性

### ✅ 支持的验证类型
- **Cloudflare Turnstile** 验证复选框
- **通用人机验证** 复选框
- **iframe内嵌** 验证元素
- **按钮形式** 的验证元素

### 🎯 智能检测
- 自动检测页面中的验证相关文本
- 支持多种选择器匹配验证元素
- 检查元素可见性和可点击性
- 处理iframe中的验证元素

### ⚙️ 可配置选项
- 启用/禁用自动点击功能
- 自定义等待时间
- 配置重试次数
- 详细日志控制

## 配置说明

### 配置文件位置
```
captcha-solvers/turnstile/config/autoClick.js
```

### 主要配置项

#### 基础配置
```javascript
{
  enabled: true,              // 是否启用自动点击功能
  waitBeforeClick: 1000,      // 点击前等待时间（毫秒）
  waitAfterClick: 2000,       // 点击后等待时间（毫秒）
  maxRetries: 3,              // 最大重试次数
  verboseLogging: true        // 是否启用详细日志
}
```

#### Cloudflare验证配置
```javascript
cloudflare: {
  checkInterval: 3000,        // 检测间隔（毫秒）
  maxWaitTime: 90,           // 最大等待时间（秒）
  handleIframes: true,       // 是否处理iframe中的验证
  verificationKeywords: [    // 验证文本关键词
    'verify', 'human', 'robot', 'captcha',
    '确认您是真实用户', 'i am human'
  ]
}
```

#### Turnstile验证配置
```javascript
turnstile: {
  enabled: true,             // 是否启用Turnstile自动点击
  waitBeforeClick: 500,      // 点击前等待时间（毫秒）
  waitAfterClick: 1500,      // 点击后等待时间（毫秒）
  handleIframes: true        // 是否处理iframe中的Turnstile
}
```

## 使用方法

### 1. 默认启用
自动点击功能默认启用，无需额外配置即可使用。

### 2. 禁用自动点击
如果需要禁用自动点击功能：

```javascript
// 修改配置文件
const autoClickConfig = {
  enabled: false,  // 禁用所有自动点击
  // ... 其他配置
};

// 或者只禁用Turnstile自动点击
turnstile: {
  enabled: false,
  // ... 其他配置
}
```

### 3. 自定义等待时间
根据网络环境调整等待时间：

```javascript
{
  waitBeforeClick: 2000,  // 增加点击前等待时间
  waitAfterClick: 3000,   // 增加点击后等待时间
}
```

## 支持的选择器

### Cloudflare验证选择器
- `input[type="checkbox"]`
- `input[name="cf-turnstile-response"]`
- `.cf-turnstile input`
- `[data-sitekey] input`
- `input[type="checkbox"][id*="verify"]`
- `input[type="checkbox"][class*="human"]`
- `.cf-turnstile`
- `[data-sitekey]`
- `.captcha-container`
- `button[id*="verify"]`
- `iframe[src*="challenges.cloudflare.com"]`

### Turnstile验证选择器
- `.cf-turnstile input[type="checkbox"]`
- `.cf-turnstile`
- `[data-sitekey]`
- `input[name="cf-turnstile-response"]`
- `.captcha-checkbox`
- `.verification-checkbox`
- `.turnstile-wrapper`

## 日志说明

### 启用详细日志
```javascript
verboseLogging: true
```

### 日志示例
```
ℹ️ [2024-01-20T10:30:00.000Z] AutoClick: 检查页面中的验证元素...
ℹ️ [2024-01-20T10:30:01.000Z] AutoClick: 检测到验证相关文本，尝试查找可点击元素...
ℹ️ [2024-01-20T10:30:02.000Z] AutoClick: 找到可见的验证元素: .cf-turnstile
ℹ️ [2024-01-20T10:30:02.500Z] AutoClick: 成功点击验证元素: .cf-turnstile
ℹ️ [2024-01-20T10:30:04.500Z] AutoClick: 页面内容发生变化，验证可能已触发
```

## 故障排除

### 1. 自动点击不工作
- 检查 `enabled` 配置是否为 `true`
- 确认页面包含支持的验证元素
- 查看日志输出确定问题原因

### 2. 点击速度过快/过慢
- 调整 `waitBeforeClick` 和 `waitAfterClick` 时间
- 根据网络延迟适当增加等待时间

### 3. 某些验证元素无法识别
- 检查页面HTML结构
- 在配置中添加新的选择器
- 启用详细日志查看检测过程

### 4. iframe验证问题
- 确认 `handleIframes` 设置为 `true`
- 检查iframe的URL是否包含预期的域名

## API使用示例

### cf5s请求
```javascript
const response = await fetch('http://localhost:3000/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    type: "cf5s",
    websiteUrl: "https://example.com"
  })
});
```

### cftoken请求
```javascript
const response = await fetch('http://localhost:3000/', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    type: "cftoken",
    websiteUrl: "https://example.com",
    websiteKey: "0x4AAAAAABA4JXCaw9E2Py-9"
  })
});
```

## 注意事项

1. **合规使用**：请确保在合法合规的范围内使用此功能
2. **性能影响**：自动点击会增加少量处理时间
3. **成功率**：并非所有验证都能自动处理，复杂验证仍需人工干预
4. **更新维护**：随着验证机制更新，可能需要调整选择器配置

## 更新日志

### v2.1.3
- ✅ 新增自动点击"确认您是真实用户"复选框功能
- ✅ 支持Cloudflare Turnstile自动验证
- ✅ 添加可配置的自动点击选项
- ✅ 支持iframe内验证元素处理
- ✅ 增加详细日志和错误处理
