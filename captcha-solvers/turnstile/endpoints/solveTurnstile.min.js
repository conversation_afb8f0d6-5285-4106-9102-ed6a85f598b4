const fs = require("fs");

async function solveTurnstileMin({ url, proxy, siteKey }) {
  if (!url) {
    throw new Error("Missing url parameter");
  }
  if (!siteKey) {
    throw new Error("Missing siteKey parameter");
  }

  // 检查浏览器是否已初始化
  if (!global.browser) {
    if (global.browserInitFailed) {
      throw new Error("浏览器初始化失败，请检查Chrome安装和配置");
    }
    throw new Error("浏览器正在初始化中，请稍后重试");
  }

  let context = null;
  let page = null;
  let isResolved = false;
  let timeoutId = null;
  let usingContextPool = false;

  // 统一的清理函数
  const cleanup = async () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (page) {
      try {
        await page.close();
      } catch (e) {
        console.error('Error closing page:', e.message);
      }
      page = null;
    }

    if (context) {
      try {
        if (usingContextPool && global.contextPool && typeof global.contextPool.releaseContext === 'function') {
          // 使用上下文池释放
          await global.contextPool.releaseContext(context);
        } else {
          // 直接关闭上下文
          await context.close();
        }
      } catch (e) {
        console.error('Error releasing context:', e.message);
      }
      context = null;
    }
  };

  try {
    // 使用上下文池获取上下文
    if (global.contextPool && typeof global.contextPool.getContext === 'function') {
      context = await global.contextPool.getContext();
      usingContextPool = true;
    } else {
      // 回退到直接创建
      context = await global.browser.createBrowserContext({
        proxyServer: proxy ? `http://${proxy.host}:${proxy.port}` : undefined,
      });
      usingContextPool = false;
    }

    if (!context) {
      throw new Error("Failed to create browser context");
    }

    // 设置超时处理
    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(async () => {
        if (!isResolved) {
          isResolved = true;
          await cleanup();
          reject(new Error("Timeout Error"));
        }
      }, global.timeOut || 180000);
    });

    // 主要逻辑Promise
    const mainPromise = new Promise(async (resolve, reject) => {
      try {
        page = await context.newPage();

        // 设置页面视口大小
        await page.setViewport({ width: 400, height: 300 });

        // 设置页面超时
        await page.setDefaultTimeout(30000);
        await page.setDefaultNavigationTimeout(30000);

        if (proxy?.username && proxy?.password) {
          await page.authenticate({
            username: proxy.username,
            password: proxy.password,
          });
        }

        await page.setRequestInterception(true);

        // 定义请求处理器
        const requestHandler = async (request) => {
          try {
            if (
              [url, url + "/"].includes(request.url()) &&
              request.resourceType() === "document"
            ) {
              await request.respond({
                status: 200,
                contentType: "text/html",
                body: String(
                  require("fs").readFileSync(require("path").join(__dirname, "../data/fakePage.html"))
                ).replace(/<site-key>/g, siteKey),
              });
            } else {
              await request.continue();
            }
          } catch (e) {
            console.error('Request handler error:', e.message);
          }
        };

        page.on("request", requestHandler);

        // 优化页面加载和token等待
        await page.goto(url, {
          waitUntil: "domcontentloaded",
          timeout: 30000
        });

        // 优化的token等待逻辑，减少阻塞时间
        let token = null;
        const maxAttempts = 2; // 减少重试次数，提高并发
        const baseTimeout = 45000; // 减少基础超时到45秒

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
          try {
            console.log(`🔄 Turnstile attempt ${attempt}/${maxAttempts} for ${new URL(url).hostname}`);

            // 并行等待Turnstile脚本和token
            const tokenPromise = page.waitForSelector('[name="cf-response"]', {
              timeout: baseTimeout + (attempt * 5000) // 较小的递增时间
            });

            const scriptPromise = page.waitForFunction(() => {
              return window.turnstile && typeof window.turnstile.render === 'function';
            }, { timeout: 10000 }).catch(() => {
              console.log('⚠️  Turnstile script not loaded quickly, continuing...');
            });

            // 并行等待，提高效率
            await Promise.race([
              tokenPromise,
              scriptPromise.then(() => tokenPromise)
            ]);

            // 快速验证token
            token = await page.evaluate(() => {
              const element = document.querySelector('[name="cf-response"]');
              const value = element?.value;

            // 优化的token验证
            if (value && value.length > 10 &&
                !value.includes('undefined') &&
                !value.includes('null') &&
                value.indexOf('.') > 0) { // Turnstile token通常包含点
              return value;
            }
            return null;
          });

          if (token) {
            console.log(`✅ Turnstile token obtained on attempt ${attempt} (${token.length} chars)`);
            break;
          } else {
            console.log(`❌ Invalid token on attempt ${attempt}, retrying...`);
            if (attempt < maxAttempts) {
              // 更短的等待时间
              await new Promise(resolve => setTimeout(resolve, 1000));
              // 快速刷新
              await page.reload({ waitUntil: 'domcontentloaded', timeout: 15000 });
            }
          }

        } catch (attemptError) {
          console.log(`❌ Attempt ${attempt} failed: ${attemptError.message}`);
          if (attempt === maxAttempts) {
            throw attemptError;
          }
          // 更快的失败恢复
          await page.reload({ waitUntil: 'domcontentloaded', timeout: 15000 }).catch(() => {});
          await new Promise(resolve => setTimeout(resolve, 1500));
        }
      }

      if (!token || token.length < 10) {
        throw new Error("Failed to get token");
      }

      isResolved = true;
      await cleanup();
      resolve(token);

    } catch (e) {
      if (!isResolved) {
        isResolved = true;
        await cleanup();
        reject(new Error(`Turnstile solving error: ${e.message}`));
      }
    }
  });

  // 使用Promise.race处理超时
  return await Promise.race([mainPromise, timeoutPromise]);

  } catch (error) {
    if (!isResolved) {
      isResolved = true;
      await cleanup();
    }
    throw error;
  }
}
module.exports = solveTurnstileMin;