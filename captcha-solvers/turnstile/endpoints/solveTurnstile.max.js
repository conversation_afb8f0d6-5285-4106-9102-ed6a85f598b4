const fs = require("fs");
const { isAutoClickEnabled, getSelectors, getConfig, log } = require('../config/autoClick');

/**
 * 处理Turnstile交互 - 自动点击验证复选框 (Max版本)
 * @param {Object} page - Puppeteer页面对象
 */
async function handleTurnstileInteractionMax(page) {
  try {
    // 检查是否启用自动点击
    if (!isAutoClickEnabled('turnstile')) {
      log('Turnstile自动点击功能已禁用 (Max版本)', 'info');
      return false;
    }

    const config = getConfig('turnstile');
    await new Promise(resolve => setTimeout(resolve, config.waitBeforeClick || 1000));

    const turnstileSelectors = getSelectors('turnstile');

    log('检查Turnstile验证元素 (Max版本)...', 'info');

    const pageContent = await page.content();
    const hasTurnstileContent = pageContent.includes('turnstile') ||
                               pageContent.includes('cf-turnstile') ||
                               pageContent.includes('data-sitekey');

    if (hasTurnstileContent) {
      log('检测到Turnstile相关内容 (Max版本)', 'info');

      for (const selector of turnstileSelectors) {
        try {
          const elements = await page.$$(selector);

          for (const element of elements) {
            const isVisible = await page.evaluate(el => {
              const style = window.getComputedStyle(el);
              const rect = el.getBoundingClientRect();
              return style.display !== 'none' &&
                     style.visibility !== 'hidden' &&
                     rect.width > 0 && rect.height > 0;
            }, element);

            if (isVisible) {
              log(`找到可见的Turnstile元素 (Max): ${selector}`, 'info');
              await element.click();
              log(`成功点击Turnstile元素 (Max): ${selector}`, 'info');
              await new Promise(resolve => setTimeout(resolve, config.waitAfterClick || 2000));
              return true;
            }
          }
        } catch (clickError) {
          log(`点击失败 (Max) ${selector}: ${clickError.message}`, 'warn');
        }
      }
    }

    return false;
  } catch (error) {
    log(`Turnstile自动交互出错 (Max): ${error.message}`, 'error');
    return false;
  }
}

function solveTurnstileMin({ url, proxy }) {
  return new Promise(async (resolve, reject) => {
    if (!url) return reject("Missing url parameter");

    // 检查浏览器是否已初始化
    if (!global.browser) {
      if (global.browserInitFailed) {
        return reject("浏览器初始化失败，请检查Chrome安装和配置");
      }
      return reject("浏览器正在初始化中，请稍后重试");
    }

    const context = await global.browser
      .createBrowserContext({
        proxyServer: proxy ? `http://${proxy.host}:${proxy.port}` : undefined, // https://pptr.dev/api/puppeteer.browsercontextoptions
      })
      .catch(() => null);

    if (!context) return reject("Failed to create browser context");

    let isResolved = false;

    var cl = setTimeout(async () => {
      if (!isResolved) {
        await context.close();
        reject("Timeout Error");
      }
    }, global.timeOut || 60000);

    try {
      const page = await context.newPage();

      if (proxy?.username && proxy?.password)
        await page.authenticate({
          username: proxy.username,
          password: proxy.password,
        });

      // 等待页面准备好后再注入脚本，避免"Requesting main frame too early"错误
      try {
        await page.evaluateOnNewDocument(() => {
          let token = null;
          async function waitForToken() {
            while (!token) {
              try {
                token = window.turnstile.getResponse();
              } catch (e) {}
              await new Promise((resolve) => setTimeout(resolve, 500));
            }
            var c = document.createElement("input");
            c.type = "hidden";
            c.name = "cf-response";
            c.value = token;
            document.body.appendChild(c);
          }
          waitForToken();
        });
      } catch (error) {
        console.log('Warning: Failed to inject turnstile script:', error.message);
        // 继续执行，稍后在页面加载后手动注入
      }

      await page.goto(url, {
        waitUntil: "domcontentloaded",
      });

      // 如果之前的脚本注入失败，在页面加载后手动注入
      try {
        const hasScript = await page.evaluate(() => {
          return document.querySelector('[name="cf-response"]') !== null;
        });

        if (!hasScript) {
          console.log('Manually injecting turnstile script after page load...');
          await page.evaluate(() => {
            let token = null;
            async function waitForToken() {
              while (!token) {
                try {
                  token = window.turnstile.getResponse();
                } catch (e) {}
                await new Promise((resolve) => setTimeout(resolve, 500));
              }
              var c = document.createElement("input");
              c.type = "hidden";
              c.name = "cf-response";
              c.value = token;
              document.body.appendChild(c);
            }
            waitForToken();
          });
        }
      } catch (error) {
        console.log('Warning: Manual script injection failed:', error.message);
      }

      // 尝试自动点击Turnstile验证
      try {
        await handleTurnstileInteractionMax(page);
      } catch (interactionError) {
        console.log('⚠️ Turnstile自动交互失败 (Max):', interactionError.message);
      }

      await page.waitForSelector('[name="cf-response"]', {
        timeout: 60000,
      });
      const token = await page.evaluate(() => {
        try {
          return document.querySelector('[name="cf-response"]').value;
        } catch (e) {
          return null;
        }
      });
      isResolved = true;
      clearInterval(cl);
      await context.close();
      if (!token || token.length < 10) return reject("Failed to get token");
      return resolve(token);
    } catch (e) {
      console.log(e);

      if (!isResolved) {
        await context.close();
        clearInterval(cl);
        reject(e.message);
      }
    }
  });
}
module.exports = solveTurnstileMin;
