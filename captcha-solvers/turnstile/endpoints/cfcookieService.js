/**
 * CF Cookie Service - 专门提取 cf_clearance cookie
 * 使用上下文池优化版本，兼容 puppeteer-real-browser
 */

const { isAutoClickEnabled, getSelectors, getConfig, log } = require('../config/autoClick');

/**
 * 处理交互式验证 - 自动点击"确认您是真实用户"复选框
 * @param {Object} page - Puppeteer页面对象
 */
async function handleInteractiveVerification(page) {
  try {
    // 检查是否启用自动点击
    if (!isAutoClickEnabled('cloudflare')) {
      log('自动点击功能已禁用', 'info');
      return false;
    }

    const config = getConfig('cloudflare');

    // 等待页面稳定
    await new Promise(resolve => setTimeout(resolve, config.waitBeforeClick || 1000));

    // 从配置获取选择器
    const checkboxSelectors = getSelectors('cloudflare');

    log('检查页面中的验证元素...', 'info');

    // 检查是否有验证相关的文本
    const pageText = await page.evaluate(() => document.body.innerText.toLowerCase());
    const verificationKeywords = config.verificationKeywords || [];
    const hasVerificationText = verificationKeywords.some(keyword =>
      pageText.includes(keyword.toLowerCase())
    );

    if (hasVerificationText) {
      log('检测到验证相关文本，尝试查找可点击元素...', 'info');
    }

    // 尝试每个选择器
    for (const selector of checkboxSelectors) {
      try {
        // 检查元素是否存在且可见
        const element = await page.$(selector);
        if (element) {
          const isVisible = await page.evaluate(el => {
            const style = window.getComputedStyle(el);
            const rect = el.getBoundingClientRect();
            return style.display !== 'none' &&
                   style.visibility !== 'hidden' &&
                   style.opacity !== '0' &&
                   rect.width > 0 &&
                   rect.height > 0;
          }, element);

          if (isVisible) {
            log(`找到可见的验证元素: ${selector}`, 'info');

            // 尝试点击元素
            await element.click();
            log(`成功点击验证元素: ${selector}`, 'info');

            // 等待一下让验证处理
            await new Promise(resolve => setTimeout(resolve, config.waitAfterClick || 2000));

            // 检查是否有变化
            const newPageText = await page.evaluate(() => document.body.innerText);
            if (newPageText !== pageText) {
              log('页面内容发生变化，验证可能已触发', 'info');
            }

            return true;
          }
        }
      } catch (clickError) {
        // 继续尝试下一个选择器
        log(`点击 ${selector} 失败: ${clickError.message}`, 'warn');
      }
    }

    // 尝试处理iframe中的验证
    if (config.handleIframes) {
      try {
        const frames = await page.frames();
        for (const frame of frames) {
          if (frame.url().includes('challenges.cloudflare.com') ||
              frame.url().includes('turnstile')) {
            log('检查iframe中的验证元素...', 'info');

            for (const selector of checkboxSelectors.slice(0, 10)) { // 只尝试前10个选择器
              try {
                const element = await frame.$(selector);
                if (element) {
                  await element.click();
                  log(`成功点击iframe中的验证元素: ${selector}`, 'info');
                  await new Promise(resolve => setTimeout(resolve, config.waitAfterClick || 2000));
                  return true;
                }
              } catch (frameError) {
                // 继续尝试
              }
            }
          }
        }
      } catch (iframeError) {
        log(`iframe处理失败: ${iframeError.message}`, 'warn');
      }
    }

    log('未找到可点击的验证元素', 'info');
    return false;

  } catch (error) {
    log(`自动验证处理出错: ${error.message}`, 'error');
    return false;
  }
}
async function getCfClearance({ url, proxy }) {
  if (!url) {
    throw new Error("Missing url parameter");
  }

  // 检查浏览器是否已初始化
  if (!global.browser) {
    if (global.browserInitFailed) {
      throw new Error("浏览器初始化失败，请检查Chrome安装和配置");
    }
    throw new Error("浏览器正在初始化中，请稍后重试");
  }

  let context = null;
  let page = null;
  let isResolved = false;
  let timeoutId = null;

  const cleanup = async () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (page) {
      try {
        await page.close();
      } catch (e) {
        console.error('Error closing page:', e.message);
      }
      page = null;
    }

    if (context) {
      try {
        // cfcookie请求需要强制关闭上下文，不能复用
        // 因为cookie状态会影响后续请求
        console.log('🧹 强制关闭cfcookie上下文以避免cookie缓存');
        await context.close();
      } catch (e) {
        console.error("Error closing cfcookie context:", e.message);
      }
      context = null;
    }
  };

  try {
    // 设置超时处理
    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(async () => {
        if (!isResolved) {
          isResolved = true;
          await cleanup();
          reject(new Error("Timeout Error - cf_clearance cookie not obtained"));
        }
      }, global.timeOut || 120000);
    });

    // 主要逻辑Promise
    const mainPromise = new Promise(async (resolve, reject) => {
      try {

        // cfcookie请求总是创建全新的上下文，避免cookie缓存问题
        console.log('🆕 为cfcookie请求创建全新上下文');
        context = await global.browser.createBrowserContext({
          proxyServer: proxy ? `http://${proxy.host}:${proxy.port}` : undefined,
        });

        if (!context) {
          throw new Error("Failed to create browser context");
        }

        page = await context.newPage();

        // 设置页面视口大小
        await page.setViewport({ width: 400, height: 300 });

        // 设置页面超时
        await page.setDefaultTimeout(30000);
        await page.setDefaultNavigationTimeout(30000);

        if (proxy?.username && proxy?.password) {
          await page.authenticate({
            username: proxy.username,
            password: proxy.password,
          });
        }

        console.log(`正在访问: ${url}`);

        // 直接访问页面
        await page.goto(url, {
          waitUntil: "domcontentloaded",
          timeout: 30000
        });

        // 等待页面完全加载，让 Cloudflare 有时间设置 cookie
        console.log('等待页面加载和 Cloudflare 验证...');

        let maxWaitTime = 90; // 等待90秒
        let checkInterval = 3; // 每3秒检查一次，减少频率

        for (let i = 0; i < maxWaitTime / checkInterval; i++) {
          if (isResolved) break;

          await new Promise(resolve => setTimeout(resolve, checkInterval * 1000));

          try {
            // 检查 cf_clearance cookie
            const cookies = await page.cookies();
            const cfClearanceCookie = cookies.find(cookie => cookie.name === 'cf_clearance');

            if (cfClearanceCookie && cfClearanceCookie.value) {
              console.log('✅ 成功获取 cf_clearance cookie');

              // 获取完整的请求头信息
              const headers = {
                'User-Agent': await page.evaluate(() => navigator.userAgent),
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
              'Accept-Language': 'en-US,en;q=0.9',
              'Accept-Encoding': 'gzip, deflate, br',
              'DNT': '1',
              'Connection': 'keep-alive',
              'Upgrade-Insecure-Requests': '1',
              'Sec-Fetch-Dest': 'document',
              'Sec-Fetch-Mode': 'navigate',
              'Sec-Fetch-Site': 'none',
              'Sec-Fetch-User': '?1',
              'Cache-Control': 'max-age=0'
            };

            // 获取所有cookies，不仅仅是cf_clearance
            const allCookies = cookies.map(cookie => ({
              name: cookie.name,
              value: cookie.value,
              domain: cookie.domain,
              path: cookie.path,
              expires: cookie.expires,
              httpOnly: cookie.httpOnly,
              secure: cookie.secure,
              sameSite: cookie.sameSite
            }));

            const result = {
              cf_clearance: cfClearanceCookie.value,
              headers: headers,
              cookies: allCookies,
              url: url,
              timestamp: new Date().toISOString()
            };

            isResolved = true;
            await cleanup();
            resolve(result);
            return;
          }

          // 检查页面状态 - 简化检查
          const content = await page.content();
          const isCloudflareChallenge = content.includes('Just a moment') ||
                                      content.includes('cf-browser-verification') ||
                                      content.includes('Checking if the site connection is secure') ||
                                      content.includes('DDoS protection by Cloudflare') ||
                                      content.includes('Ray ID:');

          if (isCloudflareChallenge) {
            console.log(`⏳ Cloudflare 验证中... (${i * checkInterval}/${maxWaitTime}s)`);

            // 尝试自动点击验证复选框
            try {
              await handleInteractiveVerification(page);
            } catch (clickError) {
              console.log('⚠️ 自动点击处理失败:', clickError.message);
            }
          } else {
            console.log(`🔍 页面已加载，等待 cf_clearance cookie... (${i * checkInterval}/${maxWaitTime}s)`);

            // 如果页面已经加载完成但没有验证页面，可能需要刷新一下
            if (i > 5 && i % 10 === 0) {
              console.log('🔄 尝试刷新页面以触发 Cloudflare 验证...');
              await page.reload({ waitUntil: 'domcontentloaded' }).catch(() => {});
            }
          }

        } catch (e) {
          console.error('检查过程中发生错误:', e.message);
          // 继续等待，不立即退出
        }
      }

      // 如果循环结束仍未找到 cookie
      if (!isResolved) {
        isResolved = true;
        await cleanup();
        reject(new Error('cf_clearance cookie not found after waiting'));
      }

    } catch (e) {
      if (!isResolved) {
        isResolved = true;
        await cleanup();
        reject(new Error(`CF clearance error: ${e.message}`));
      }
    }
  });

  // 使用Promise.race处理超时
  return await Promise.race([mainPromise, timeoutPromise]);

  } catch (error) {
    if (!isResolved) {
      isResolved = true;
      await cleanup();
    }
    throw error;
  }
}

module.exports = getCfClearance;