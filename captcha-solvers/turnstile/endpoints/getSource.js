async function getSource({ url, proxy }) {
  if (!url) {
    throw new Error("Missing url parameter");
  }

  let context = null;
  let page = null;
  let isResolved = false;
  let timeoutId = null;
  let requestHandler = null;
  let responseHandler = null;

  // 统一的清理函数
  const cleanup = async () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    if (page) {
      try {
        // 移除事件监听器防止内存泄漏
        if (requestHandler) page.off('request', requestHandler);
        if (responseHandler) page.off('response', responseHandler);
        await page.close();
      } catch (e) {
        console.error('Error closing page:', e.message);
      }
      page = null;
    }

    if (context) {
      try {
        await context.close();
      } catch (e) {
        console.error('Error closing context:', e.message);
      }
      context = null;
    }
  };

  try {
    // 创建浏览器上下文
    context = await global.browser.createBrowserContext({
      proxyServer: proxy ? `http://${proxy.host}:${proxy.port}` : undefined,
    });

    if (!context) {
      throw new Error("Failed to create browser context");
    }

    // 设置超时处理
    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(async () => {
        if (!isResolved) {
          isResolved = true;
          await cleanup();
          reject(new Error("Timeout Error"));
        }
      }, global.timeOut || 60000);
    });

    // 主要逻辑Promise
    const mainPromise = new Promise(async (resolve, reject) => {
      try {
        page = await context.newPage();

        // 设置页面超时
        await page.setDefaultTimeout(30000);
        await page.setDefaultNavigationTimeout(30000);

        if (proxy?.username && proxy?.password) {
          await page.authenticate({
            username: proxy.username,
            password: proxy.password,
          });
        }

        await page.setRequestInterception(true);

        // 定义事件处理器
        requestHandler = async (request) => {
          try {
            await request.continue();
          } catch (e) {
            console.error('Request handler error:', e.message);
          }
        };

        responseHandler = async (res) => {
          try {
            if (!isResolved &&
              [200, 302].includes(res.status()) &&
              [url, url + "/"].includes(res.url())
            ) {
              await page
                .waitForNavigation({ waitUntil: "load", timeout: 5000 })
                .catch(() => {});
              const html = await page.content();

              isResolved = true;
              await cleanup();
              resolve(html);
            }
          } catch (e) {
            if (!isResolved) {
              isResolved = true;
              await cleanup();
              reject(new Error(`Response handler error: ${e.message}`));
            }
          }
        };

        page.on("request", requestHandler);
        page.on("response", responseHandler);

        await page.goto(url, {
          waitUntil: "domcontentloaded",
          timeout: 30000
        });
      } catch (e) {
        if (!isResolved) {
          isResolved = true;
          await cleanup();
          reject(new Error(`Page operation error: ${e.message}`));
        }
      }
    });

    // 使用Promise.race处理超时
    return await Promise.race([mainPromise, timeoutPromise]);

  } catch (error) {
    if (!isResolved) {
      isResolved = true;
      await cleanup();
    }
    throw error;
  }
}
module.exports = getSource;
