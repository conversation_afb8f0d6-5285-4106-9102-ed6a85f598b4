/**
 * 自动点击配置
 * 控制cf5s和cftoken服务的自动验证点击功能
 */

const autoClickConfig = {
  // 是否启用自动点击功能
  enabled: true,
  
  // 点击前的等待时间（毫秒）
  waitBeforeClick: 1000,
  
  // 点击后的等待时间（毫秒）
  waitAfterClick: 2000,
  
  // 最大重试次数
  maxRetries: 3,
  
  // 是否启用详细日志
  verboseLogging: true,
  
  // Cloudflare验证相关配置
  cloudflare: {
    // 检测间隔（毫秒）
    checkInterval: 3000,
    
    // 最大等待时间（秒）
    maxWaitTime: 90,
    
    // 是否处理iframe中的验证
    handleIframes: true,
    
    // 验证文本关键词
    verificationKeywords: [
      'verify',
      'human',
      'robot',
      'captcha',
      '确认您是真实用户',
      'i am human',
      'prove you are human',
      'just a moment',
      'checking if the site connection is secure'
    ]
  },
  
  // Turnstile验证相关配置
  turnstile: {
    // 是否启用Turnstile自动点击
    enabled: true,
    
    // 点击前等待时间（毫秒）
    waitBeforeClick: 500,
    
    // 点击后等待时间（毫秒）
    waitAfterClick: 1500,
    
    // 是否处理iframe中的Turnstile
    handleIframes: true
  },
  
  // 选择器配置
  selectors: {
    // Cloudflare验证选择器
    cloudflare: [
      'input[type="checkbox"]',
      'input[name="cf-turnstile-response"]',
      '.cf-turnstile input',
      '[data-sitekey] input',
      'input[type="checkbox"][id*="verify"]',
      'input[type="checkbox"][id*="human"]',
      'input[type="checkbox"][id*="captcha"]',
      'input[type="checkbox"][class*="verify"]',
      'input[type="checkbox"][class*="human"]',
      'input[type="checkbox"][class*="captcha"]',
      '.cf-turnstile',
      '[data-sitekey]',
      '.captcha-container',
      '.verification-container',
      'button[type="submit"]',
      'button[id*="verify"]',
      'button[class*="verify"]',
      'iframe[src*="challenges.cloudflare.com"]',
      'iframe[src*="turnstile"]'
    ],
    
    // Turnstile验证选择器
    turnstile: [
      '.cf-turnstile input[type="checkbox"]',
      '.cf-turnstile',
      '[data-sitekey]',
      'input[name="cf-turnstile-response"]',
      'input[type="checkbox"]',
      '.captcha-checkbox',
      '.verification-checkbox',
      '.turnstile-wrapper',
      '.captcha-container',
      '.challenge-container'
    ]
  }
};

/**
 * 获取配置
 * @param {string} section - 配置节名称
 * @returns {Object} 配置对象
 */
function getConfig(section = null) {
  if (section) {
    return autoClickConfig[section] || {};
  }
  return autoClickConfig;
}

/**
 * 更新配置
 * @param {string} section - 配置节名称
 * @param {Object} newConfig - 新配置
 */
function updateConfig(section, newConfig) {
  if (autoClickConfig[section]) {
    autoClickConfig[section] = { ...autoClickConfig[section], ...newConfig };
  }
}

/**
 * 检查是否启用自动点击
 * @param {string} type - 验证类型 ('cloudflare' 或 'turnstile')
 * @returns {boolean} 是否启用
 */
function isAutoClickEnabled(type = 'cloudflare') {
  if (!autoClickConfig.enabled) {
    return false;
  }
  
  if (type === 'turnstile') {
    return autoClickConfig.turnstile.enabled;
  }
  
  return true;
}

/**
 * 获取选择器列表
 * @param {string} type - 验证类型
 * @returns {Array} 选择器数组
 */
function getSelectors(type) {
  return autoClickConfig.selectors[type] || [];
}

/**
 * 记录日志（如果启用详细日志）
 * @param {string} message - 日志消息
 * @param {string} level - 日志级别
 */
function log(message, level = 'info') {
  if (autoClickConfig.verboseLogging) {
    const timestamp = new Date().toISOString();
    const prefix = level === 'error' ? '❌' : level === 'warn' ? '⚠️' : 'ℹ️';
    console.log(`${prefix} [${timestamp}] AutoClick: ${message}`);
  }
}

module.exports = {
  getConfig,
  updateConfig,
  isAutoClickEnabled,
  getSelectors,
  log,
  
  // 导出默认配置供外部使用
  defaultConfig: autoClickConfig
};
