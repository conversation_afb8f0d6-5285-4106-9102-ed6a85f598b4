#!/usr/bin/env node
/**
 * 测试cftoken和cf5s功能
 */

const TEST_CONFIG = {
    server: {
        host: 'localhost',
        port: 3000,
        timeout: 120000
    }
};

/**
 * 测试cf5s功能
 */
async function testCf5s() {
    console.log('🧪 测试cf5s功能...');
    console.log('URL: https://remix.campnetwork.xyz/');
    
    const startTime = Date.now();
    
    try {
        const response = await fetch(`http://${TEST_CONFIG.server.host}:${TEST_CONFIG.server.port}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: 'cf5s',
                websiteUrl: 'https://remix.campnetwork.xyz/'
            }),
            signal: AbortSignal.timeout(TEST_CONFIG.server.timeout)
        });

        const result = await response.json();
        const duration = Date.now() - startTime;
        
        console.log(`📋 cf5s测试结果 (耗时: ${Math.round(duration/1000)}s):`);
        console.log(`   状态码: ${response.status}`);
        console.log(`   响应码: ${result.code}`);
        console.log(`   消息: ${result.message}`);
        
        if (result.headers && result.headers.cookie) {
            const cookieMatch = result.headers.cookie.match(/cf_clearance=([^;]+)/);
            if (cookieMatch) {
                console.log(`   ✅ 成功获取cf_clearance: ${cookieMatch[1].substring(0, 50)}...`);
                console.log(`   🍪 完整cookie长度: ${result.headers.cookie.length} 字符`);
                console.log(`   🌐 User-Agent: ${result.headers['user-agent']}`);
                return { success: true, result, duration };
            } else {
                console.log(`   ❌ cookie中未找到cf_clearance`);
                return { success: false, error: 'No cf_clearance in cookie', duration };
            }
        } else {
            console.log(`   ❌ 响应中没有headers或cookie`);
            return { success: false, error: 'No headers or cookie', duration };
        }
        
    } catch (error) {
        const duration = Date.now() - startTime;
        console.log(`   ❌ cf5s测试失败 (耗时: ${Math.round(duration/1000)}s): ${error.message}`);
        return { success: false, error: error.message, duration };
    }
}

/**
 * 测试cftoken功能
 */
async function testCftoken() {
    console.log('\n🧪 测试cftoken功能...');
    console.log('URL: https://app.gata.xyz');
    console.log('SiteKey: 0x4AAAAAABBBMWmeu7r0C-oA');
    
    const startTime = Date.now();
    
    try {
        const response = await fetch(`http://${TEST_CONFIG.server.host}:${TEST_CONFIG.server.port}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: 'cftoken',
                websiteUrl: 'https://app.gata.xyz',
                websiteKey: '0x4AAAAAABBBMWmeu7r0C-oA'
            }),
            signal: AbortSignal.timeout(TEST_CONFIG.server.timeout)
        });

        const result = await response.json();
        const duration = Date.now() - startTime;
        
        console.log(`📋 cftoken测试结果 (耗时: ${Math.round(duration/1000)}s):`);
        console.log(`   状态码: ${response.status}`);
        console.log(`   响应码: ${result.code}`);
        
        if (result.token) {
            console.log(`   ✅ 成功获取token: ${result.token.substring(0, 50)}...`);
            console.log(`   🎫 Token长度: ${result.token.length} 字符`);
            
            // 验证token格式
            if (result.token.length > 100 && result.token.includes('.')) {
                console.log(`   ✅ Token格式验证通过`);
                return { success: true, result, duration };
            } else {
                console.log(`   ⚠️  Token格式可能异常`);
                return { success: true, result, duration, warning: 'Token format unusual' };
            }
        } else {
            console.log(`   ❌ 响应中没有token`);
            console.log(`   消息: ${result.message || 'No message'}`);
            return { success: false, error: result.message || 'No token', duration };
        }
        
    } catch (error) {
        const duration = Date.now() - startTime;
        console.log(`   ❌ cftoken测试失败 (耗时: ${Math.round(duration/1000)}s): ${error.message}`);
        return { success: false, error: error.message, duration };
    }
}

/**
 * 检查浏览器窗口尺寸效果
 */
async function checkBrowserWindow() {
    console.log('\n🖥️  浏览器窗口尺寸检查:');
    console.log('   当前设置: 600x400 像素');
    console.log('   位置: (100, 100)');
    console.log('   💡 请观察打开的浏览器窗口是否比之前小');
}

/**
 * 运行功能测试
 */
async function runFunctionalityTests() {
    console.log('🔧 功能测试');
    console.log('='.repeat(80));
    
    // 检查浏览器窗口
    await checkBrowserWindow();
    
    // 测试cf5s
    const cf5sResult = await testCf5s();
    
    // 等待一下
    console.log('\n⏳ 等待5秒后进行下一个测试...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // 测试cftoken
    const cftokenResult = await testCftoken();
    
    // 生成报告
    console.log('\n📋 功能测试报告');
    console.log('='.repeat(80));
    
    console.log(`cf5s功能: ${cf5sResult.success ? '✅ 正常' : '❌ 异常'} (${Math.round(cf5sResult.duration/1000)}s)`);
    if (!cf5sResult.success) {
        console.log(`   错误: ${cf5sResult.error}`);
    }
    
    console.log(`cftoken功能: ${cftokenResult.success ? '✅ 正常' : '❌ 异常'} (${Math.round(cftokenResult.duration/1000)}s)`);
    if (!cftokenResult.success) {
        console.log(`   错误: ${cftokenResult.error}`);
    }
    if (cftokenResult.warning) {
        console.log(`   警告: ${cftokenResult.warning}`);
    }
    
    const allSuccess = cf5sResult.success && cftokenResult.success;
    
    if (allSuccess) {
        console.log('\n🎉 所有功能测试通过！');
        console.log('   - cf5s可以正常获取cf_clearance cookie');
        console.log('   - cftoken可以正常获取Turnstile token');
        console.log('   - 浏览器窗口尺寸已调整为更小尺寸');
    } else {
        console.log('\n⚠️  部分功能存在问题，请检查日志');
    }
    
    console.log('\n💡 监控面板: http://localhost:3000/monitor/');
    
    return {
        cf5sResult,
        cftokenResult,
        allSuccess
    };
}

// 运行测试
if (require.main === module) {
    runFunctionalityTests().catch(error => {
        console.error('测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = { testCf5s, testCftoken, runFunctionalityTests };
