#!/usr/bin/env node
/**
 * 测试协议超时修复效果
 */

const TEST_CONFIG = {
    server: {
        host: 'localhost',
        port: 3000,
        timeout: 60000
    },
    stress: {
        concurrent: 5,  // 并发请求数
        total: 20,      // 总请求数
        delay: 1000     // 请求间隔
    }
};

/**
 * 发送单个测试请求
 */
async function sendTestRequest(id) {
    const startTime = Date.now();
    
    try {
        const response = await fetch(`http://${TEST_CONFIG.server.host}:${TEST_CONFIG.server.port}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                type: 'cf5s',
                websiteUrl: 'https://remix.campnetwork.xyz/'
            }),
            signal: AbortSignal.timeout(TEST_CONFIG.server.timeout)
        });

        const result = await response.json();
        const duration = Date.now() - startTime;
        
        const success = response.status === 200 && result.code === 200;
        
        console.log(`📋 请求 #${id}: ${success ? '✅' : '❌'} (${Math.round(duration/1000)}s)`);
        
        return {
            id,
            success,
            duration,
            status: response.status,
            result
        };
        
    } catch (error) {
        const duration = Date.now() - startTime;
        console.log(`📋 请求 #${id}: ❌ 失败 (${Math.round(duration/1000)}s) - ${error.message}`);
        
        return {
            id,
            success: false,
            duration,
            error: error.message
        };
    }
}

/**
 * 并发压力测试
 */
async function runStressTest() {
    console.log('🔧 协议超时修复压力测试');
    console.log('='.repeat(80));
    console.log(`并发数: ${TEST_CONFIG.stress.concurrent}`);
    console.log(`总请求数: ${TEST_CONFIG.stress.total}`);
    console.log(`请求间隔: ${TEST_CONFIG.stress.delay}ms`);
    console.log('='.repeat(80));
    
    const results = [];
    const startTime = Date.now();
    
    // 分批发送请求
    for (let batch = 0; batch < Math.ceil(TEST_CONFIG.stress.total / TEST_CONFIG.stress.concurrent); batch++) {
        const batchStart = batch * TEST_CONFIG.stress.concurrent;
        const batchEnd = Math.min(batchStart + TEST_CONFIG.stress.concurrent, TEST_CONFIG.stress.total);
        
        console.log(`\n🚀 批次 ${batch + 1}: 发送请求 ${batchStart + 1}-${batchEnd}`);
        
        // 并发发送这一批请求
        const batchPromises = [];
        for (let i = batchStart; i < batchEnd; i++) {
            batchPromises.push(sendTestRequest(i + 1));
        }
        
        const batchResults = await Promise.allSettled(batchPromises);
        
        // 处理结果
        batchResults.forEach((result, index) => {
            if (result.status === 'fulfilled') {
                results.push(result.value);
            } else {
                results.push({
                    id: batchStart + index + 1,
                    success: false,
                    duration: 0,
                    error: result.reason.message
                });
            }
        });
        
        // 批次间延迟
        if (batch < Math.ceil(TEST_CONFIG.stress.total / TEST_CONFIG.stress.concurrent) - 1) {
            console.log(`⏳ 等待 ${TEST_CONFIG.stress.delay}ms 后继续下一批次...`);
            await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.stress.delay));
        }
    }
    
    const totalDuration = Date.now() - startTime;
    
    return { results, totalDuration };
}

/**
 * 分析测试结果
 */
function analyzeResults(results, totalDuration) {
    console.log('\n📊 测试结果分析');
    console.log('='.repeat(80));
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    console.log(`总请求数: ${results.length}`);
    console.log(`成功请求: ${successful.length} (${Math.round(successful.length / results.length * 100)}%)`);
    console.log(`失败请求: ${failed.length} (${Math.round(failed.length / results.length * 100)}%)`);
    console.log(`总耗时: ${Math.round(totalDuration / 1000)}s`);
    
    if (successful.length > 0) {
        const durations = successful.map(r => r.duration);
        const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
        const minDuration = Math.min(...durations);
        const maxDuration = Math.max(...durations);
        
        console.log(`\n⏱️  响应时间统计:`);
        console.log(`   平均: ${Math.round(avgDuration / 1000)}s`);
        console.log(`   最快: ${Math.round(minDuration / 1000)}s`);
        console.log(`   最慢: ${Math.round(maxDuration / 1000)}s`);
    }
    
    if (failed.length > 0) {
        console.log(`\n❌ 失败原因分析:`);
        const errorTypes = {};
        failed.forEach(r => {
            const errorType = r.error || 'Unknown';
            errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
        });
        
        Object.entries(errorTypes).forEach(([error, count]) => {
            console.log(`   ${error}: ${count} 次`);
        });
    }
    
    // 判断修复效果
    const successRate = successful.length / results.length;
    const hasProtocolTimeouts = failed.some(r => 
        r.error && r.error.includes('Network.enable timed out')
    );
    
    console.log('\n🔍 修复效果评估:');
    if (successRate >= 0.9 && !hasProtocolTimeouts) {
        console.log('✅ 协议超时问题已修复！');
        console.log('   - 成功率 >= 90%');
        console.log('   - 无协议超时错误');
    } else if (hasProtocolTimeouts) {
        console.log('⚠️  仍存在协议超时问题');
        console.log('   - 检测到 Network.enable 超时错误');
    } else if (successRate < 0.9) {
        console.log('⚠️  成功率偏低，可能存在其他问题');
        console.log(`   - 成功率: ${Math.round(successRate * 100)}%`);
    }
    
    return {
        successRate,
        hasProtocolTimeouts,
        avgDuration: successful.length > 0 ? successful.reduce((a, b) => a + b.duration, 0) / successful.length : 0
    };
}

/**
 * 运行完整测试
 */
async function runProtocolTimeoutTest() {
    console.log('🧪 开始协议超时修复测试...\n');
    
    const { results, totalDuration } = await runStressTest();
    const analysis = analyzeResults(results, totalDuration);
    
    console.log('\n💡 测试建议:');
    if (analysis.successRate >= 0.9 && !analysis.hasProtocolTimeouts) {
        console.log('🎉 系统运行良好，可以处理高并发请求');
    } else {
        console.log('🔧 建议进一步优化或调整配置');
    }
    
    console.log('\n📋 监控面板: http://localhost:3000/monitor/');
    
    return analysis;
}

// 运行测试
if (require.main === module) {
    runProtocolTimeoutTest().catch(error => {
        console.error('测试运行失败:', error);
        process.exit(1);
    });
}

module.exports = { runStressTest, analyzeResults, runProtocolTimeoutTest };
