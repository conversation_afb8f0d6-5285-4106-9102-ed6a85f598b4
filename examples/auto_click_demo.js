/**
 * 自动点击功能演示
 * 展示如何使用cf5s和cftoken服务的自动验证点击功能
 */

const axios = require('axios');

// 服务配置
const SERVICE_URL = 'http://localhost:3000';

/**
 * 演示cf5s自动点击功能
 */
async function demoCf5sAutoClick() {
  console.log('\n🚀 演示 CF5S 自动点击功能');
  console.log('=' .repeat(40));
  
  try {
    const startTime = Date.now();
    
    const response = await axios.post(SERVICE_URL, {
      type: 'cf5s',
      websiteUrl: 'https://loyalty.campnetwork.xyz/home'
    }, {
      timeout: 120000 // 2分钟超时
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (response.data.code === 200) {
      console.log('✅ CF5S 自动验证成功!');
      console.log(`⏱️  处理时间: ${duration}ms`);
      console.log(`🍪 cf_clearance: ${response.data.cf_clearance?.substring(0, 20)}...`);
      console.log(`🌐 User-Agent: ${response.data.headers?.['User-Agent']?.substring(0, 50)}...`);
      console.log(`📊 Cookies数量: ${response.data.cookies?.length || 0}`);
    } else {
      console.log(`❌ CF5S 验证失败: ${response.data.message}`);
    }
    
  } catch (error) {
    console.log(`❌ CF5S 请求出错: ${error.message}`);
    if (error.response?.data) {
      console.log(`📊 错误详情: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

/**
 * 演示cftoken自动点击功能
 */
async function demoCfTokenAutoClick() {
  console.log('\n🚀 演示 CFToken 自动点击功能');
  console.log('=' .repeat(40));
  
  try {
    const startTime = Date.now();
    
    const response = await axios.post(SERVICE_URL, {
      type: 'cftoken',
      websiteUrl: 'https://turnstile.zeroclover.io/',
      websiteKey: '0x4AAAAAAAEwzhD6pyKkgXC0'
    }, {
      timeout: 120000 // 2分钟超时
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (response.data.code === 200) {
      console.log('✅ CFToken 自动验证成功!');
      console.log(`⏱️  处理时间: ${duration}ms`);
      console.log(`🎫 Token: ${response.data.token?.substring(0, 30)}...`);
      console.log(`📏 Token长度: ${response.data.token?.length || 0} 字符`);
    } else {
      console.log(`❌ CFToken 验证失败: ${response.data.message}`);
    }
    
  } catch (error) {
    console.log(`❌ CFToken 请求出错: ${error.message}`);
    if (error.response?.data) {
      console.log(`📊 错误详情: ${JSON.stringify(error.response.data, null, 2)}`);
    }
  }
}

/**
 * 检查自动点击配置
 */
async function checkAutoClickConfig() {
  console.log('\n🔧 检查自动点击配置');
  console.log('=' .repeat(40));
  
  try {
    // 尝试读取配置文件
    const { getConfig, isAutoClickEnabled } = require('../captcha-solvers/turnstile/config/autoClick');
    
    console.log('📋 当前配置状态:');
    console.log(`   总开关: ${getConfig().enabled ? '✅ 启用' : '❌ 禁用'}`);
    console.log(`   Cloudflare自动点击: ${isAutoClickEnabled('cloudflare') ? '✅ 启用' : '❌ 禁用'}`);
    console.log(`   Turnstile自动点击: ${isAutoClickEnabled('turnstile') ? '✅ 启用' : '❌ 禁用'}`);
    
    const cloudflareConfig = getConfig('cloudflare');
    const turnstileConfig = getConfig('turnstile');
    
    console.log('\n⚙️ 详细配置:');
    console.log(`   点击前等待: ${getConfig().waitBeforeClick}ms`);
    console.log(`   点击后等待: ${getConfig().waitAfterClick}ms`);
    console.log(`   处理iframe: ${cloudflareConfig.handleIframes ? '✅' : '❌'}`);
    console.log(`   详细日志: ${getConfig().verboseLogging ? '✅' : '❌'}`);
    console.log(`   支持选择器数量: ${getConfig().selectors.cloudflare.length + getConfig().selectors.turnstile.length}`);
    
  } catch (error) {
    console.log(`❌ 无法读取配置: ${error.message}`);
  }
}

/**
 * 显示使用提示
 */
function showUsageTips() {
  console.log('\n💡 使用提示');
  console.log('=' .repeat(40));
  console.log('1. 确保服务已启动: npm start');
  console.log('2. 访问监控面板: http://localhost:3000/monitor');
  console.log('3. 查看详细日志以了解自动点击过程');
  console.log('4. 如需调整配置，编辑: captcha-solvers/turnstile/config/autoClick.js');
  console.log('5. 运行测试脚本: npm run test:auto-click');
  
  console.log('\n🔧 配置选项:');
  console.log('- enabled: 启用/禁用自动点击功能');
  console.log('- waitBeforeClick: 点击前等待时间');
  console.log('- waitAfterClick: 点击后等待时间');
  console.log('- verboseLogging: 详细日志输出');
  console.log('- handleIframes: 处理iframe中的验证');
  
  console.log('\n📚 更多信息:');
  console.log('- 功能文档: docs/AUTO_CLICK_FEATURE.md');
  console.log('- API文档: docs/API.md');
  console.log('- 故障排除: docs/TROUBLESHOOTING.md');
}

/**
 * 主演示函数
 */
async function main() {
  console.log('🎯 CF Clearance Scraper 自动点击功能演示');
  console.log('🔗 https://github.com/0xsongsu/cf-clearance-scraper');
  console.log('📅 ' + new Date().toLocaleString());
  
  // 检查服务状态
  try {
    await axios.get(`${SERVICE_URL}/health`, { timeout: 5000 });
    console.log('✅ 服务运行正常');
  } catch (error) {
    console.log('❌ 服务未启动，请先运行: npm start');
    return;
  }
  
  // 检查配置
  await checkAutoClickConfig();
  
  // 演示功能
  await demoCf5sAutoClick();
  await demoCfTokenAutoClick();
  
  // 显示使用提示
  showUsageTips();
  
  console.log('\n🎉 演示完成!');
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
});

// 运行演示
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 演示运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  demoCf5sAutoClick,
  demoCfTokenAutoClick,
  checkAutoClickConfig
};
