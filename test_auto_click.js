/**
 * 自动点击功能测试脚本
 * 测试cf5s和cftoken服务的自动验证点击功能
 */

const axios = require('axios');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  timeout: 120000, // 2分钟超时
  
  // 测试用例
  testCases: [
    {
      name: 'CF5S 自动点击测试',
      type: 'cf5s',
      websiteUrl: 'https://loyalty.campnetwork.xyz/home',
      description: '测试cf5s服务的自动点击功能'
    },
    {
      name: 'CFToken 自动点击测试',
      type: 'cftoken',
      websiteUrl: 'https://turnstile.zeroclover.io/',
      websiteKey: '0x4AAAAAAAEwzhD6pyKkgXC0',
      description: '测试cftoken服务的自动点击功能'
    }
  ]
};

/**
 * 发送测试请求
 * @param {Object} testCase - 测试用例
 * @returns {Promise<Object>} 测试结果
 */
async function sendTestRequest(testCase) {
  const startTime = Date.now();
  
  try {
    console.log(`\n🚀 开始测试: ${testCase.name}`);
    console.log(`📝 描述: ${testCase.description}`);
    console.log(`🌐 URL: ${testCase.websiteUrl}`);
    
    const requestData = {
      type: testCase.type,
      websiteUrl: testCase.websiteUrl
    };
    
    // 如果是cftoken类型，添加websiteKey
    if (testCase.type === 'cftoken' && testCase.websiteKey) {
      requestData.websiteKey = testCase.websiteKey;
    }
    
    console.log(`📤 发送请求...`);
    
    const response = await axios.post(TEST_CONFIG.baseUrl, requestData, {
      timeout: TEST_CONFIG.timeout,
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    if (response.data.code === 200) {
      console.log(`✅ 测试成功!`);
      console.log(`⏱️  耗时: ${duration}ms`);
      console.log(`📊 响应数据:`, JSON.stringify(response.data, null, 2));
      
      return {
        success: true,
        testCase: testCase.name,
        duration,
        response: response.data
      };
    } else {
      console.log(`❌ 测试失败: ${response.data.message}`);
      return {
        success: false,
        testCase: testCase.name,
        duration,
        error: response.data.message
      };
    }
    
  } catch (error) {
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`❌ 测试出错: ${error.message}`);
    
    if (error.response) {
      console.log(`📊 错误响应:`, error.response.data);
    }
    
    return {
      success: false,
      testCase: testCase.name,
      duration,
      error: error.message
    };
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🔧 CF Clearance Scraper 自动点击功能测试');
  console.log('=' .repeat(50));
  
  const results = [];
  
  for (const testCase of TEST_CONFIG.testCases) {
    const result = await sendTestRequest(testCase);
    results.push(result);
    
    // 测试间隔
    if (TEST_CONFIG.testCases.indexOf(testCase) < TEST_CONFIG.testCases.length - 1) {
      console.log('\n⏳ 等待5秒后进行下一个测试...');
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  
  // 输出测试总结
  console.log('\n' + '=' .repeat(50));
  console.log('📊 测试总结');
  console.log('=' .repeat(50));
  
  const successCount = results.filter(r => r.success).length;
  const totalCount = results.length;
  
  console.log(`✅ 成功: ${successCount}/${totalCount}`);
  console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`);
  
  results.forEach(result => {
    const status = result.success ? '✅' : '❌';
    const duration = `${result.duration}ms`;
    console.log(`${status} ${result.testCase} - ${duration}`);
    
    if (!result.success) {
      console.log(`   错误: ${result.error}`);
    }
  });
  
  // 性能统计
  if (successCount > 0) {
    const successResults = results.filter(r => r.success);
    const avgDuration = successResults.reduce((sum, r) => sum + r.duration, 0) / successResults.length;
    const minDuration = Math.min(...successResults.map(r => r.duration));
    const maxDuration = Math.max(...successResults.map(r => r.duration));
    
    console.log('\n📈 性能统计:');
    console.log(`   平均耗时: ${Math.round(avgDuration)}ms`);
    console.log(`   最短耗时: ${minDuration}ms`);
    console.log(`   最长耗时: ${maxDuration}ms`);
  }
  
  console.log('\n🎉 测试完成!');
}

/**
 * 检查服务状态
 */
async function checkServiceStatus() {
  try {
    console.log('🔍 检查服务状态...');
    const response = await axios.get(`${TEST_CONFIG.baseUrl}/health`, {
      timeout: 5000
    });
    
    if (response.status === 200) {
      console.log('✅ 服务运行正常');
      return true;
    } else {
      console.log('❌ 服务状态异常');
      return false;
    }
  } catch (error) {
    console.log(`❌ 无法连接到服务: ${error.message}`);
    console.log('请确保服务已启动 (npm start)');
    return false;
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 启动自动点击功能测试...\n');
  
  // 检查服务状态
  const serviceOk = await checkServiceStatus();
  if (!serviceOk) {
    process.exit(1);
  }
  
  // 运行测试
  await runAllTests();
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的Promise拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 运行测试
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  sendTestRequest,
  runAllTests,
  checkServiceStatus
};
